from django.db import models
from accounts.models import Account
from store.models import Product

class Payment(models.Model):
    user = models.ForeignKey(Account, verbose_name='المستخدم', on_delete=models.CASCADE)
    payment_id = models.CharField('معرف الدفع', max_length=100)
    payment_method = models.CharField('طريقة الدفع', max_length=100)
    amount_paid = models.CharField('المبلغ المدفوع', max_length=100)
    status = models.CharField('الحالة', max_length=100)
    created_at = models.DateTimeField('تاريخ الإنشاء', auto_now_add=True)

    class Meta:
        verbose_name = 'دفعة'
        verbose_name_plural = 'الدفعات'

    def __str__(self):
        return self.payment_id

class Order(models.Model):
    STATUS = (
        ('New', 'جديد'),
        ('Accepted', 'مقبول'),
        ('Completed', 'مكتمل'),
        ('Cancelled', 'ملغي'),
    )

    user = models.ForeignKey(Account, verbose_name='المستخدم', on_delete=models.SET_NULL, null=True)
    payment = models.ForeignKey(Payment, verbose_name='الدفع', on_delete=models.SET_NULL, blank=True, null=True)
    order_number = models.CharField('رقم الطلب', max_length=20)
    first_name = models.CharField('الاسم الأول', max_length=50)
    last_name = models.CharField('اسم العائلة', max_length=50)
    phone = models.CharField('رقم الهاتف', max_length=15)
    email = models.EmailField('البريد الإلكتروني', max_length=50)
    address_line_1 = models.CharField('العنوان 1', max_length=50)
    address_line_2 = models.CharField('العنوان 2', max_length=50, blank=True)
    city = models.CharField('المدينة', max_length=50)
    order_note = models.CharField('ملاحظات الطلب', max_length=100, blank=True)
    order_total = models.FloatField('إجمالي الطلب')
    tax = models.FloatField('الضريبة')
    status = models.CharField('الحالة', max_length=10, choices=STATUS, default='New')
    ip = models.CharField('عنوان IP', blank=True, max_length=20)
    is_ordered = models.BooleanField('تم الطلب', default=False)
    created_at = models.DateTimeField('تاريخ الإنشاء', auto_now_add=True)
    updated_at = models.DateTimeField('تاريخ التحديث', auto_now=True)

    class Meta:
        verbose_name = 'طلب'
        verbose_name_plural = 'الطلبات'

    def full_name(self):
        return f'{self.first_name} {self.last_name}'

    def full_address(self):
        return f'{self.address_line_1} {self.address_line_2}'

    def __str__(self):
        return self.order_number

class OrderProduct(models.Model):
    order = models.ForeignKey(Order, verbose_name='الطلب', on_delete=models.CASCADE)
    payment = models.ForeignKey(Payment, verbose_name='الدفع', on_delete=models.SET_NULL, blank=True, null=True)
    user = models.ForeignKey(Account, verbose_name='المستخدم', on_delete=models.CASCADE)
    product = models.ForeignKey(Product, verbose_name='المنتج', on_delete=models.CASCADE)
    quantity = models.IntegerField('الكمية')
    product_price = models.FloatField('سعر المنتج')
    ordered = models.BooleanField('تم الطلب', default=False)
    created_at = models.DateTimeField('تاريخ الإنشاء', auto_now_add=True)
    updated_at = models.DateTimeField('تاريخ التحديث', auto_now=True)

    class Meta:
        verbose_name = 'منتج الطلب'
        verbose_name_plural = 'منتجات الطلب'

    def __str__(self):
        return self.product.name