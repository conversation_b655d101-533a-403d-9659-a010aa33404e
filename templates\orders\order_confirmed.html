{% extends 'base.html' %}
{% load static %}

{% block title %}تم تأكيد طلبك | جريت كارت{% endblock %}

{% block content %}
<!-- ========================= SECTION CONTENT ========================= -->
<section class="section-content padding-y bg">
<div class="container">

<div class="row justify-content-center">
	<div class="col-lg-8">
		<div class="card">
			<div class="card-body text-center">
				<div class="mb-4">
					{% if order.payment.payment_method == 'الدفع عند الاستلام' %}
						<i class="fas fa-check-circle text-success" style="font-size: 4rem;"></i>
						<h2 class="text-success mt-3">تم تأكيد طلبك بنجاح!</h2>
						<p class="lead">شكراً لك على طلبك. سيتم التواصل معك قريباً لتأكيد التفاصيل.</p>
						
						<div class="alert alert-info">
							<h5><strong>رقم الطلب:</strong> {{ order_number }}</h5>
							<p><strong>طريقة الدفع:</strong> الدفع عند الاستلام 💰</p>
							<p><strong>حالة الطلب:</strong> 
								<span class="badge badge-warning">{{ order.get_status_display }}</span>
							</p>
						</div>
						
						<div class="alert alert-warning">
							<h6><i class="fas fa-info-circle"></i> ملاحظة مهمة:</h6>
							<p class="mb-0">سيتم تأكيد طلبك من قبل فريق المبيعات خلال 24 ساعة. سنتواصل معك على رقم الهاتف المسجل لتأكيد التفاصيل وموعد التسليم.</p>
						</div>
					{% else %}
						<i class="fas fa-credit-card text-success" style="font-size: 4rem;"></i>
						<h2 class="text-success mt-3">تم الدفع بنجاح!</h2>
						<p class="lead">شكراً لك على طلبك. تم استلام الدفع بنجاح.</p>
						
						<div class="alert alert-success">
							<h5><strong>رقم الطلب:</strong> {{ order_number }}</h5>
							<p><strong>رقم المعاملة:</strong> {{ transID }}</p>
							<p><strong>طريقة الدفع:</strong> {{ order.payment.payment_method }}</p>
							<p><strong>حالة الطلب:</strong> 
								<span class="badge badge-success">{{ order.get_status_display }}</span>
							</p>
						</div>
					{% endif %}
				</div>
			</div>
		</div>
	</div>
</div>

<!-- تفاصيل الطلب -->
<div class="row mt-4">
	<div class="col-lg-8">
		<div class="card">
			<div class="card-header">
				<h5>تفاصيل الطلب</h5>
			</div>
			<div class="card-body">
				<div class="row">
					<div class="col-md-6">
						<h6>معلومات التوصيل:</h6>
						<p>
							<strong>{{ order.first_name }} {{ order.last_name }}</strong><br>
							{{ order.address_line_1 }}<br>
							{% if order.address_line_2 %}{{ order.address_line_2 }}<br>{% endif %}
							{{ order.city }}<br>
							<strong>الهاتف:</strong> {{ order.phone }}<br>
							<strong>البريد الإلكتروني:</strong> {{ order.email }}
						</p>
					</div>
					<div class="col-md-6">
						<h6>معلومات الدفع:</h6>
						<p>
							<strong>طريقة الدفع:</strong> {{ order.payment.payment_method }}<br>
							<strong>حالة الدفع:</strong> 
							{% if order.payment.status == 'Pending' %}
								<span class="badge badge-warning">{{ order.payment.get_status_display }}</span>
							{% else %}
								<span class="badge badge-success">{{ order.payment.get_status_display }}</span>
							{% endif %}<br>
							<strong>المبلغ الإجمالي:</strong> {{ order.order_total }} جنيه
						</p>
					</div>
				</div>
				
				{% if order.order_note %}
				<div class="row mt-3">
					<div class="col-12">
						<h6>ملاحظات الطلب:</h6>
						<p>{{ order.order_note }}</p>
					</div>
				</div>
				{% endif %}
			</div>
		</div>
	</div>
	
	<div class="col-lg-4">
		<div class="card">
			<div class="card-header">
				<h5>ملخص الطلب</h5>
			</div>
			<div class="card-body">
				<dl class="dlist-align">
				  <dt>المجموع الفرعي:</dt>
				  <dd class="text-right">{{ subtotal }} جنيه</dd>
				</dl>
				<dl class="dlist-align">
				  <dt>الضرائب:</dt>
				  <dd class="text-right">{{ order.tax }} جنيه</dd>
				</dl>
				<dl class="dlist-align">
				  <dt>المجموع الإجمالي:</dt>
				  <dd class="text-right text-dark b"><strong>{{ order.order_total }} جنيه</strong></dd>
				</dl>
			</div>
		</div>
		
		<!-- الخطوات التالية -->
		<div class="card mt-3">
			<div class="card-header">
				<h5>الخطوات التالية</h5>
			</div>
			<div class="card-body">
				{% if order.payment.payment_method == 'الدفع عند الاستلام' %}
					<div class="timeline">
						<div class="timeline-item completed">
							<i class="fas fa-check text-success"></i>
							<span>تم استلام الطلب</span>
						</div>
						<div class="timeline-item current">
							<i class="fas fa-phone text-warning"></i>
							<span>سيتم التواصل معك للتأكيد</span>
						</div>
						<div class="timeline-item">
							<i class="fas fa-box text-muted"></i>
							<span>تحضير الطلب</span>
						</div>
						<div class="timeline-item">
							<i class="fas fa-truck text-muted"></i>
							<span>الشحن والتسليم</span>
						</div>
						<div class="timeline-item">
							<i class="fas fa-money-bill text-muted"></i>
							<span>الدفع عند الاستلام</span>
						</div>
					</div>
				{% else %}
					<div class="timeline">
						<div class="timeline-item completed">
							<i class="fas fa-check text-success"></i>
							<span>تم الدفع</span>
						</div>
						<div class="timeline-item current">
							<i class="fas fa-box text-warning"></i>
							<span>تحضير الطلب</span>
						</div>
						<div class="timeline-item">
							<i class="fas fa-truck text-muted"></i>
							<span>الشحن والتسليم</span>
						</div>
					</div>
				{% endif %}
			</div>
		</div>
	</div>
</div>

<!-- المنتجات المطلوبة -->
<div class="row mt-4">
	<div class="col-12">
		<div class="card">
			<div class="card-header">
				<h5>المنتجات المطلوبة</h5>
			</div>
			<div class="card-body">
				{% for item in ordered_products %}
				<div class="row border-bottom py-2">
					<div class="col-md-2">
						{% if item.product.image %}
							<img src="{{ item.product.image.url }}" class="img-fluid" style="max-height: 60px;">
						{% else %}
							<img src="{% static 'images/default-product.png' %}" class="img-fluid" style="max-height: 60px;">
						{% endif %}
					</div>
					<div class="col-md-6">
						<h6>{{ item.product.product_name }}</h6>
						{% if item.variations.all %}
							<small class="text-muted">
								{% for variation in item.variations.all %}
									{{ variation.variation_category | capfirst }}: {{ variation.variation_value | capfirst }}
									{% if not forloop.last %}, {% endif %}
								{% endfor %}
							</small>
						{% endif %}
					</div>
					<div class="col-md-2 text-center">
						<span>الكمية: {{ item.quantity }}</span>
					</div>
					<div class="col-md-2 text-right">
						<span>{{ item.product_price }} جنيه</span>
					</div>
				</div>
				{% endfor %}
			</div>
		</div>
	</div>
</div>

<!-- أزرار التنقل -->
<div class="row mt-4">
	<div class="col-12 text-center">
		<a href="{% url 'store:home' %}" class="btn btn-primary">العودة للصفحة الرئيسية</a>
		<a href="{% url 'store:store' %}" class="btn btn-outline-primary">متابعة التسوق</a>
	</div>
</div>

</div> <!-- container .//  -->
</section>
<!-- ========================= SECTION CONTENT END// ========================= -->

<style>
.timeline {
    list-style: none;
    padding: 0;
}

.timeline-item {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    padding: 10px;
    border-radius: 5px;
}

.timeline-item.completed {
    background-color: #d4edda;
}

.timeline-item.current {
    background-color: #fff3cd;
}

.timeline-item i {
    margin-left: 10px;
    width: 20px;
}
</style>

{% endblock %}
