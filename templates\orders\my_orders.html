{% extends 'base.html' %}
{% load static %}

{% block title %}طلباتي | جريت كارت{% endblock %}

{% block content %}
<!-- ========================= SECTION CONTENT ========================= -->
<section class="section-content padding-y bg">
<div class="container">

<div class="row">
	<div class="col-lg-12">
		<div class="card">
			<div class="card-header">
				<h4><i class="fas fa-shopping-bag"></i> طلباتي</h4>
			</div>
			<div class="card-body">
				{% if orders %}
					<div class="table-responsive">
						<table class="table table-hover">
							<thead class="thead-light">
								<tr>
									<th>رقم الطلب</th>
									<th>تاريخ الطلب</th>
									<th>المبلغ الإجمالي</th>
									<th>طريقة الدفع</th>
									<th>حالة الطلب</th>
									<th>حالة الدفع</th>
									<th>الإجراءات</th>
								</tr>
							</thead>
							<tbody>
								{% for order in orders %}
								<tr>
									<td>
										<strong>#{{ order.order_number }}</strong>
									</td>
									<td>
										{{ order.created_at|date:"d/m/Y" }}<br>
										<small class="text-muted">{{ order.created_at|date:"H:i" }}</small>
									</td>
									<td>
										<strong>{{ order.order_total }} جنيه</strong>
									</td>
									<td>
										{% if order.payment %}
											{{ order.payment.payment_method }}
										{% else %}
											غير محدد
										{% endif %}
									</td>
									<td>
										<span class="badge badge-{{ order.get_status_color }}">
											{{ order.get_status_display }}
										</span>
									</td>
									<td>
										<span class="badge badge-{{ order.get_payment_status_color }}">
											{{ order.get_payment_status_display }}
										</span>
									</td>
									<td>
										<div class="btn-group" role="group">
											<a href="{% url 'orders:track_order' %}?order_number={{ order.order_number }}"
											   class="btn btn-sm btn-outline-primary" title="تتبع الطلب">
												<i class="fas fa-search"></i>
											</a>
											<button type="button" class="btn btn-sm btn-outline-info"
													onclick="showOrderDetails('{{ order.order_number }}')" title="تفاصيل الطلب">
												<i class="fas fa-eye"></i>
											</button>
											{% if order.can_be_cancelled %}
											<button type="button" class="btn btn-sm btn-outline-danger"
													onclick="cancelOrder('{{ order.order_number }}')" title="إلغاء الطلب">
												<i class="fas fa-times"></i>
											</button>
											{% endif %}
										</div>
									</td>
								</tr>
								{% endfor %}
							</tbody>
						</table>
					</div>

					<!-- Pagination إذا كان هناك طلبات كثيرة -->
					{% if orders.count > 10 %}
					<nav aria-label="Page navigation">
						<ul class="pagination justify-content-center">
							<!-- يمكن إضافة pagination هنا لاحقاً -->
						</ul>
					</nav>
					{% endif %}

				{% else %}
					<!-- لا توجد طلبات -->
					<div class="text-center py-5">
						<i class="fas fa-shopping-bag text-muted" style="font-size: 4rem;"></i>
						<h4 class="mt-3">لا توجد طلبات بعد</h4>
						<p class="lead text-muted">لم تقم بأي طلبات حتى الآن</p>
						<a href="{% url 'store:store' %}" class="btn btn-primary">
							<i class="fas fa-shopping-cart"></i> ابدأ التسوق الآن
						</a>
					</div>
				{% endif %}
			</div>
		</div>

		<!-- إحصائيات سريعة -->
		{% if orders %}
		<div class="row mt-4">
			<div class="col-md-3">
				<div class="card text-center">
					<div class="card-body">
						<h5 class="card-title">{{ orders.count }}</h5>
						<p class="card-text">إجمالي الطلبات</p>
					</div>
				</div>
			</div>
			<div class="col-md-3">
				<div class="card text-center">
					<div class="card-body">
						<h5 class="card-title">{{ orders|length }}</h5>
						<p class="card-text">الطلبات المكتملة</p>
					</div>
				</div>
			</div>
			<div class="col-md-3">
				<div class="card text-center">
					<div class="card-body">
						{% with pending_orders=orders|length %}
						<h5 class="card-title">
							{% for order in orders %}
								{% if order.status == 'Pending' or order.status == 'Confirmed' %}{{ forloop.counter0|add:1 }}{% endif %}
							{% endfor %}
						</h5>
						{% endwith %}
						<p class="card-text">طلبات قيد المعالجة</p>
					</div>
				</div>
			</div>
			<div class="col-md-3">
				<div class="card text-center">
					<div class="card-body">
						{% with total_spent=0 %}
						{% for order in orders %}
							{% with total_spent=total_spent|add:order.order_total %}{% endwith %}
						{% endfor %}
						<h5 class="card-title">
							{% widthratio orders.0.order_total 1 orders.count %} جنيه
						</h5>
						{% endwith %}
						<p class="card-text">إجمالي المشتريات</p>
					</div>
				</div>
			</div>
		</div>
		{% endif %}
	</div>
</div>

</div> <!-- container .//  -->
</section>
<!-- ========================= SECTION CONTENT END// ========================= -->

<!-- Modal لتفاصيل الطلب -->
<div class="modal fade" id="orderDetailsModal" tabindex="-1" role="dialog">
	<div class="modal-dialog modal-lg" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title">تفاصيل الطلب</h5>
				<button type="button" class="close" data-dismiss="modal">
					<span>&times;</span>
				</button>
			</div>
			<div class="modal-body" id="orderDetailsContent">
				<!-- سيتم تحميل المحتوى هنا -->
			</div>
		</div>
	</div>
</div>

<script>
// عرض تفاصيل الطلب
function showOrderDetails(orderNumber) {
	// يمكن إضافة AJAX call هنا لجلب تفاصيل الطلب
	$('#orderDetailsModal').modal('show');
	$('#orderDetailsContent').html('<p>جاري تحميل تفاصيل الطلب...</p>');

	// للآن، سنوجه المستخدم لصفحة التتبع
	setTimeout(function() {
		window.location.href = "{% url 'orders:track_order' %}?order_number=" + orderNumber;
	}, 500);
}

// إلغاء الطلب
function cancelOrder(orderNumber) {
	if (confirm('هل أنت متأكد من إلغاء هذا الطلب؟')) {
		// يمكن إضافة AJAX call هنا لإلغاء الطلب
		alert('سيتم إضافة وظيفة إلغاء الطلب قريباً');
	}
}

// تحديث الصفحة كل 30 ثانية لرؤية تحديثات الحالة
setInterval(function() {
	// يمكن إضافة تحديث تلقائي هنا
}, 30000);
</script>

<style>
.badge {
	font-size: 0.8em;
}

.btn-group .btn {
	margin-right: 2px;
}

.table td {
	vertical-align: middle;
}

.card {
	box-shadow: 0 2px 4px rgba(0,0,0,0.1);
	border: none;
}

.card-header {
	background-color: #f8f9fa;
	border-bottom: 1px solid #dee2e6;
}
</style>

{% endblock %}
