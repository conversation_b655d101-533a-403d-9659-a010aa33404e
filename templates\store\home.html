<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>جريت كارت | الصفحة الرئيسية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="text-center">
            <h1 class="mb-4">مرحباً بكم في جريت كارت</h1>
            <p class="lead">متجرك الإلكتروني المفضل</p>
            
            <div class="row mt-5">
                <div class="col-12">
                    <h2>المنتجات المتاحة</h2>
                </div>
            </div>
            
            <div class="row">
                {% if products %}
                    {% for product in products %}
                    <div class="col-md-4 mb-4">
                        <div class="card">
                            {% if product.image %}
                            <img src="{{ product.image.url }}" class="card-img-top" alt="{{ product.name }}" style="height: 200px; object-fit: cover;">
                            {% endif %}
                            <div class="card-body">
                                <h5 class="card-title">{{ product.name }}</h5>
                                <p class="card-text">{{ product.description|truncatewords:10 }}</p>
                                <p class="text-primary fw-bold">{{ product.price }} ريال</p>
                                <a href="#" class="btn btn-primary">عرض التفاصيل</a>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="col-12">
                        <div class="alert alert-info">
                            <h4>لا توجد منتجات متاحة حالياً</h4>
                            <p>يرجى المحاولة لاحقاً</p>
                        </div>
                    </div>
                {% endif %}
            </div>
            
            <div class="mt-5">
                <a href="/store/" class="btn btn-success btn-lg">تصفح جميع المنتجات</a>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
