<!DOCTYPE HTML>
<html lang="ar" dir="rtl">
<head>
<meta charset="utf-8">
<meta http-equiv="pragma" content="no-cache" />
<meta http-equiv="cache-control" content="max-age=604800" />
<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

<title>جريت كارت | سلة التسوق</title>

<link href="images/favicon.ico" rel="shortcut icon" type="image/x-icon">

<!-- jQuery -->
<script src="js/jquery-2.0.0.min.js" type="text/javascript"></script>

<!-- Bootstrap4 files-->
<script src="js/bootstrap.bundle.min.js" type="text/javascript"></script>
<link href="css/bootstrap.css" rel="stylesheet" type="text/css"/>

<!-- Font awesome 5 -->
<link href="fonts/fontawesome/css/all.min.css" type="text/css" rel="stylesheet">

<!-- custom style -->
<link href="css/ui.css" rel="stylesheet" type="text/css"/>
<link href="css\responsive.css" rel="stylesheet" media="only screen and (max-width: 1200px)" />
<link href="css\rtl-arabic.css" rel="stylesheet" type="text/css"/>

<!-- custom javascript -->
<script src="js/script.js" type="text/javascript"></script>
<script src="js/arabic-translation.js" type="text/javascript"></script>

<script type="text/javascript">
/// some script

// jquery ready start
$(document).ready(function() {
	// jQuery code

});
// jquery end
</script>

</head>
<body>


<header class="section-header">
<nav class="navbar p-md-0 navbar-expand-sm navbar-light border-bottom">
<div class="container">
  <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarTop4" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
    <span class="navbar-toggler-icon"></span>
  </button>
  <div class="collapse navbar-collapse" id="navbarTop4">
    <ul class="navbar-nav mr-auto">
    	<li class="nav-item dropdown">
		 	<a href="#" class="nav-link dropdown-toggle" data-toggle="dropdown">   Language </a>
		    <ul class="dropdown-menu small">
				<li><a class="dropdown-item" href="#">English</a></li>
				<li><a class="dropdown-item" href="#">Arabic</a></li>
				<li><a class="dropdown-item" href="#">Russian </a></li>
		    </ul>
		</li>
		<li class="nav-item dropdown">
			<a href="#" class="nav-link dropdown-toggle" data-toggle="dropdown"> EGP </a>
			<ul class="dropdown-menu small">
				<li><a class="dropdown-item" href="#">USD</a></li>
				<li><a class="dropdown-item" href="#">EUR</a></li>
				<li><a class="dropdown-item" href="#">SAR</a></li>
		    </ul>
		</li>
    </ul>
    <ul class="navbar-nav">
		<li><a href="#" class="nav-link"> <i class="fa fa-envelope"></i> Email </a></li>
		<li><a href="#" class="nav-link"> <i class="fa fa-phone"></i> Call us </a></li>
	</ul> <!-- list-inline //  -->
  </div> <!-- navbar-collapse .// -->
</div> <!-- container //  -->
</nav>

<section class="header-main border-bottom">
	<div class="container">
<div class="row align-items-center">
	<div class="col-lg-2 col-md-3 col-6">
		<a href="./" class="brand-wrap">
			<img class="logo" src="./images/logo.png">
		</a> <!-- brand-wrap.// -->
	</div>
	<div class="col-lg col-sm col-md col-6 flex-grow-0">
		<div class="category-wrap dropdown d-inline-block float-right">
			<button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown">
				<i class="fa fa-bars"></i> All category
			</button>
			<div class="dropdown-menu">
				<a class="dropdown-item" href="#">Machinery / Mechanical Parts / Tools </a>
				<a class="dropdown-item" href="#">Consumer Electronics / Home Appliances </a>
				<a class="dropdown-item" href="#">Auto / Transportation</a>
				<a class="dropdown-item" href="#">Apparel / Textiles / Timepieces </a>
				<a class="dropdown-item" href="#">Home & Garden / Construction / Lights </a>
				<a class="dropdown-item" href="#">Beauty & Personal Care / Health </a>
			</div>
		</div>  <!-- category-wrap.// -->
	</div> <!-- col.// -->
	<a href="./store.html" class="btn btn-outline-primary">Store</a>
	<div class="col-lg  col-md-6 col-sm-12 col">
		<form action="#" class="search">
			<div class="input-group w-100">
			    <input type="text" class="form-control" style="width:60%;" placeholder="Search">

			    <div class="input-group-append">
			      <button class="btn btn-primary" type="submit">
			        <i class="fa fa-search"></i>
			      </button>
			    </div>
		    </div>
		</form> <!-- search-wrap .end// -->
	</div> <!-- col.// -->
	<div class="col-lg-3 col-sm-6 col-8 order-2 order-lg-3">
				<div class="d-flex justify-content-end mb-3 mb-lg-0">
					<div class="widget-header">
						<small class="title text-muted">Welcome guest!</small>
						<div>
							<a href="./signin.html">Sign in</a> <span class="dark-transp"> | </span>
							<a href="./register.html"> Register</a>
						</div>
					</div>
					<a href="./cart.html" class="widget-header pl-3 ml-3">
						<div class="icon icon-sm rounded-circle border"><i class="fa fa-shopping-cart"></i></div>
						<span class="badge badge-pill badge-danger notify">0</span>
					</a>
				</div> <!-- widgets-wrap.// -->
			</div> <!-- col.// -->
</div> <!-- row.// -->
	</div> <!-- container.// -->
</section> <!-- header-main .// -->



</header> <!-- section-header.// -->


{% extends 'base.html' %}
{% load static %}

{% block title %}سلة التسوق | جريت كارت{% endblock %}

{% block content %}

<section class="section-content padding-y bg">
<div class="container">

{% if not cart_items %}
	<h2 class="text-center">سلة التسوق فارغة</h2>
	<br>
	<div class="text-center">
		<a href="{% url 'store:store' %}" class="btn btn-primary">مواصلة التسوق</a>
	</div>
{% else %}
<div class="row">
	<aside class="col-lg-9">
<div class="card">
<table class="table table-borderless table-shopping-cart">
<thead class="text-muted">
<tr class="small text-uppercase">
  <th scope="col">المنتج</th>
  <th scope="col" width="120">الكمية</th>
  <th scope="col" width="120">السعر</th>
  <th scope="col" class="text-right" width="200"> </th>
</tr>
</thead>
<tbody>

{% for cart_item in cart_items %}
<tr>
	<td>
		<figure class="itemside align-items-center">
			<div class="aside"><img src="{{ cart_item.product.image.url }}" class="img-sm"></div>
			<figcaption class="info">
				<a href="{{ cart_item.product.get_url }}" class="title text-dark">{{ cart_item.product.name }}</a>
				<p class="text-muted small">
					{% if cart_item.variations.all %}
						{% for item in cart_item.variations.all %}
							{{ item.variation_category | capfirst }} : {{ item.variation_value | capfirst }} <br>
						{% endfor %}
					{% endif %}
				</p>
			</figcaption>
		</figure>
	</td>
	<td>
		<div class="col">
			<div class="input-group input-spinner">
				<div class="input-group-prepend">
					<a href="{% url 'cart:remove_cart' cart_item.product.id cart_item.id %}" class="btn btn-light" type="button"> <i class="fa fa-minus"></i> </a>
				</div>
				<input type="text" class="form-control" value="{{ cart_item.quantity }}" readonly>
				<div class="input-group-append">
					<a href="{% url 'cart:add_cart' cart_item.product.id %}?next=/cart/" class="btn btn-light" type="button"> <i class="fa fa-plus"></i> </a>
				</div>
			</div>
		</div>
	</td>
	<td>
		<div class="price-wrap">
			<var class="price">{{ cart_item.sub_total }} جنيه</var>
			<small class="text-muted"> {{ cart_item.product.price }} جنيه للوحدة </small>
		</div>
	</td>
	<td class="text-right">
	<a href="{% url 'cart:remove_cart_item' cart_item.product.id cart_item.id %}" onclick="return confirm('هل أنت متأكد من حذف هذا المنتج؟')" class="btn btn-danger"> حذف</a>
	</td>
</tr>
{% endfor %}

</tbody>
</table>
</div>

	</aside>
	<aside class="col-lg-3">

		<div class="card">
		<div class="card-body">
			<dl class="dlist-align">
				<dt>السعر الإجمالي:</dt>
				<dd class="text-right">{{ total }} جنيه</dd>
			</dl>
			<dl class="dlist-align">
				<dt>الضريبة:</dt>
				<dd class="text-right"> {{ tax }} جنيه</dd>
			</dl>
			<dl class="dlist-align">
				<dt>المجموع الكلي:</dt>
				<dd class="text-right text-dark b"><strong>{{ grand_total }} جنيه</strong></dd>
			</dl>
			<hr>
			<p class="text-center mb-3">
				<img src="{% static './images/misc/payments.png' %}" height="26">
			</p>
			<a href="{% url 'orders:checkout' %}" class="btn btn-primary btn-block"> إتمام الطلب </a>
			<a href="{% url 'store:store' %}" class="btn btn-light btn-block">مواصلة التسوق</a>
		</div>
		</div>

	</aside>

</div>
{% endif %}

</div>
</section>

{% endblock %}
<!-- ========================= SECTION CONTENT END// ========================= -->
</body>
</html>