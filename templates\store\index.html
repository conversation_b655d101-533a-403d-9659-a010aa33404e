{% extends 'base.html' %}
{% load static %}

{% block title %}جريت كارت | واحدة من أكبر منصات التسوق عبر الإنترنت{% endblock %}

{% block content %}

<!-- ========================= SECTION MAIN ========================= -->

<section class="section-intro padding-y-sm">
<div class="container">

<div class="intro-banner-wrap">
	<img src="{% static 'images/banners/1.jpg' %}" class="img-fluid rounded">
</div>

</div> <!-- container //  -->
</section>
<!-- ========================= SECTION MAIN END// ========================= -->

<!-- ========================= SECTION  ========================= -->
<section class="section-name padding-y-sm">
<div class="container">

<header class="section-heading">
	<a href="{% url 'store:store' %}" class="btn btn-outline-primary float-left">عرض الكل</a>
	<h3 class="section-title">المنتجات الشائعة</h3>
</header><!-- sect-heading -->


<div class="row">
	{% if products %}
		{% for product in products %}
		<div class="col-md-3">
			<div class="card card-product-grid">
				<a href="{{ product.get_url }}" class="img-wrap">
					<img src="{{ product.image.url }}" alt="{{ product.name }}">
				</a>
				<figcaption class="info-wrap">
					<a href="{{ product.get_url }}" class="title">{{ product.name }}</a>
					<div class="price mt-1">{{ product.price }} ريال</div>
				</figcaption>
			</div>
		</div>
		{% endfor %}
	{% else %}
		<div class="col-12 text-center">
			<h4>لا توجد منتجات متاحة حالياً</h4>
		</div>
	{% endif %}
</div>
</div><!-- container // -->
</section>
<!-- ========================= SECTION  END// ========================= -->




{% endblock %}