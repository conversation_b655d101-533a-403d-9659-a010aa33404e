from django.db import models
from django.contrib.auth.models import AbstractBaseUser, BaseUserManager

class MyAccountManager(BaseUserManager):
    def create_user(self, first_name, last_name, username, email, password=None):
        if not email:
            raise ValueError('يج<PERSON> أن يكون لديك عنوان بريد إلكتروني')
        if not username:
            raise ValueError('يجب أن يكون لديك اسم مستخدم')
        
        user = self.model(
            email = self.normalize_email(email),
            username = username,
            first_name = first_name,
            last_name = last_name,
        )
        user.set_password(password)
        user.save(using=self._db)
        return user
    
    def create_superuser(self, first_name, last_name, email, username, password):
        user = self.create_user(
            email = self.normalize_email(email),
            username = username,
            password = password,
            first_name = first_name,
            last_name = last_name,
        )
        user.is_admin = True
        user.is_active = True
        user.is_staff = True
        user.is_superadmin = True
        user.save(using=self._db)
        return user

class Account(AbstractBaseUser):
    first_name = models.CharField('الاسم الأول', max_length=50)
    last_name = models.CharField('اسم العائلة', max_length=50)
    username = models.CharField('اسم المستخدم', max_length=50, unique=True)
    email = models.EmailField('البريد الإلكتروني', max_length=100, unique=True)
    phone_number = models.CharField('رقم الهاتف', max_length=50)

    # required fields
    date_joined = models.DateTimeField('تاريخ التسجيل', auto_now_add=True)
    last_login = models.DateTimeField('آخر دخول', auto_now_add=True)
    is_admin = models.BooleanField('مدير', default=False)
    is_staff = models.BooleanField('موظف', default=False)
    is_active = models.BooleanField('نشط', default=False)
    is_superadmin = models.BooleanField('مدير عام', default=False)

    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['username', 'first_name', 'last_name']

    objects = MyAccountManager()

    class Meta:
        verbose_name = 'حساب'
        verbose_name_plural = 'الحسابات'

    def __str__(self):
        return self.email

    def has_perm(self, perm, obj=None):
        return self.is_admin

    def has_module_perms(self, add_label):
        return True

class UserProfile(models.Model):
    user = models.OneToOneField(Account, verbose_name='المستخدم', on_delete=models.CASCADE)
    address_line_1 = models.CharField('العنوان 1', max_length=100, blank=True)
    address_line_2 = models.CharField('العنوان 2', max_length=100, blank=True)
    profile_picture = models.ImageField('الصورة الشخصية', upload_to='userprofile', blank=True)
    city = models.CharField('المدينة', max_length=20, blank=True)

    class Meta:
        verbose_name = 'الملف الشخصي'
        verbose_name_plural = 'الملفات الشخصية'

    def __str__(self):
        return self.user.first_name

    def full_address(self):
        return f'{self.address_line_1} {self.address_line_2}'
