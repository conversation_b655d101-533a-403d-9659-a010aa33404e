{% load static %}

<!-- ========================= HEADER المحسن ========================= -->
<header class="section-header">

<!-- الصف الأول: الشعار + البحث + السلة -->
<section class="header-main border-bottom py-3 bg-white">
	<div class="container">
		<div class="row align-items-center">
			<!-- الشعار -->
			<div class="col-lg-2 col-md-3 col-6">
				<a href="{% url 'store:home' %}" class="brand-wrap">
					<img class="logo" src="{% static 'images/logo.png' %}" alt="جريت كارت" style="max-height: 50px;">
				</a>
			</div>

			<!-- شريط البحث المحسن -->
			<div class="col-lg-7 col-md-6 col-12 order-3 order-md-2 mt-3 mt-md-0">
				<div class="search-container position-relative">
					<form action="{% url 'store:search' %}" method="GET" class="search-form">
						<div class="search-wrapper">
							<div class="search-icon-left">
								<i class="fa fa-search text-muted"></i>
							</div>
							<input type="text"
								   name="keyword"
								   class="form-control search-input"
								   placeholder="ابحث عن المنتجات، العلامات التجارية، الفئات..."
								   autocomplete="off"
								   id="searchInput">
							<button class="btn search-btn" type="submit">
								<i class="fa fa-search"></i>
								<span class="d-none d-md-inline">بحث</span>
							</button>
						</div>
					</form>

					<!-- نتائج البحث المباشر -->
					<div class="search-suggestions" id="searchSuggestions" style="display: none;">
						<div class="suggestions-header">
							<i class="fa fa-clock text-muted mr-2"></i>
							<span>عمليات البحث الأخيرة</span>
						</div>
						<div class="suggestions-list">
							<!-- سيتم ملؤها بـ JavaScript -->
						</div>
					</div>
				</div>
			</div>

			<!-- السلة والحساب المحسنة -->
			<div class="col-lg-3 col-md-3 col-6 order-2 order-md-3">
				<div class="d-flex justify-content-end align-items-center">
					<!-- معلومات المستخدم -->
					{% if user.id is None %}
					<div class="user-info mr-3">
						<div class="user-greeting">
							<small class="text-muted d-block">مرحباً بك!</small>
							<div class="user-links">
								<a href="{% url 'accounts:login' %}" class="btn btn-sm btn-outline-primary">دخول</a>
								<a href="{% url 'accounts:register' %}" class="btn btn-sm btn-primary">تسجيل</a>
							</div>
						</div>
					</div>
					{% else %}
					<div class="user-info mr-3">
						<div class="user-greeting">
							<small class="text-muted d-block">مرحباً {{ user.first_name }}!</small>
							<div class="user-links">
								<a href="{% url 'accounts:dashboard' %}" class="text-primary small">حسابي</a> |
								<a href="{% url 'accounts:logout' %}" class="text-danger small">خروج</a>
							</div>
						</div>
					</div>
					{% endif %}

					<!-- سلة التسوق المحسنة -->
					<div class="cart-container position-relative">
						<a href="{% url 'cart:cart' %}" class="cart-link" id="cartToggle">
							<div class="cart-icon-wrapper">
								<i class="fa fa-shopping-cart cart-icon-large"></i>
								{% if cart_count > 0 %}
								<span class="cart-badge-new">{{ cart_count }}</span>
								{% endif %}
							</div>
						</a>

						<!-- معاينة سريعة للسلة -->
						<div class="cart-dropdown" id="cartDropdown" style="display: none;">
							<div class="cart-dropdown-header">
								<h6 class="mb-0">سلة التسوق</h6>
								<span class="cart-count">{{ cart_count }} منتج</span>
							</div>
							<div class="cart-dropdown-body">
								{% if cart_count > 0 %}
								<div class="cart-items">
									<!-- سيتم ملؤها بـ AJAX -->
									<div class="text-center py-3">
										<i class="fa fa-spinner fa-spin"></i>
										<p class="mb-0">جاري التحميل...</p>
									</div>
								</div>
								<div class="cart-dropdown-footer">
									<a href="{% url 'cart:cart' %}" class="btn btn-outline-primary btn-sm btn-block">عرض السلة</a>
									<a href="{% url 'orders:checkout' %}" class="btn btn-primary btn-sm btn-block">إتمام الشراء</a>
								</div>
								{% else %}
								<div class="empty-cart text-center py-4">
									<i class="fa fa-shopping-cart fa-3x text-muted mb-3"></i>
									<p class="text-muted">سلة التسوق فارغة</p>
									<a href="{% url 'store:store' %}" class="btn btn-primary btn-sm">ابدأ التسوق</a>
								</div>
								{% endif %}
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</section>

<!-- الصف الثاني: الأيقونات والروابط -->
<section class="header-nav bg-light py-2 border-bottom">
	<div class="container">
		<div class="row align-items-center">
			<!-- فئات المنتجات -->
			<div class="col-lg-3 col-md-4 col-6">
				<div class="category-wrap dropdown">
					<button type="button" class="btn btn-outline-primary btn-sm dropdown-toggle w-100" data-toggle="dropdown">
						<i class="fa fa-bars mr-2"></i> جميع الفئات
					</button>
					<div class="dropdown-menu w-100">
						<a class="dropdown-item" href="{% url 'store:store' %}">
							<i class="fa fa-tag mr-2 text-primary"></i>جميع المنتجات
						</a>
						<!-- يمكن إضافة الفئات هنا لاحقاً -->
					</div>
				</div>
			</div>

			<!-- روابط سريعة -->
			<div class="col-lg-6 col-md-4 col-6">
				<nav class="d-flex justify-content-center">
					<a href="{% url 'store:store' %}" class="nav-link text-dark mx-2">
						<i class="fa fa-store mr-1 text-primary"></i> المتجر
					</a>
					{% if user.id is not None %}
					<a href="{% url 'orders:my_orders' %}" class="nav-link text-dark mx-2">
						<i class="fa fa-list-alt mr-1 text-primary"></i> طلباتي
					</a>
					<a href="{% url 'orders:track_order' %}" class="nav-link text-dark mx-2">
						<i class="fa fa-search mr-1 text-primary"></i> تتبع الطلب
					</a>
					{% endif %}
				</nav>
			</div>

			<!-- معلومات التواصل -->
			<div class="col-lg-3 col-md-4 col-12">
				<div class="d-flex justify-content-end">
					<a href="tel:+201234567890" class="nav-link text-dark small mx-1">
						<i class="fa fa-phone mr-1 text-success"></i> اتصل بنا
					</a>
					<a href="mailto:<EMAIL>" class="nav-link text-dark small mx-1">
						<i class="fa fa-envelope mr-1 text-info"></i> راسلنا
					</a>
				</div>
			</div>
		</div>
	</div>
</section>

</header>
<!-- ========================= HEADER END// ========================= -->
