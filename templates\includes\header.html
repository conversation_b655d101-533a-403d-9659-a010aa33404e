{% load static %}
<header class="section-header">
<nav class="navbar p-md-0 navbar-expand-sm navbar-light border-bottom">
<div class="container">
  <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarTop4" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
    <span class="navbar-toggler-icon"></span>
  </button>
  <div class="collapse navbar-collapse" id="navbarTop4">
    <ul class="navbar-nav mr-auto">
    	<li class="nav-item dropdown">
		 	<a href="#" class="nav-link dropdown-toggle" data-toggle="dropdown">اللغة</a>
		    <ul class="dropdown-menu small">
				<li><a class="dropdown-item" href="#">العربية</a></li>
				<li><a class="dropdown-item" href="#">English</a></li>
		    </ul>
		</li>
		<li class="nav-item dropdown">
			<a href="#" class="nav-link dropdown-toggle" data-toggle="dropdown">جنيه</a>
			<ul class="dropdown-menu small">
				<li><a class="dropdown-item" href="#">دولار</a></li>
				<li><a class="dropdown-item" href="#">يورو</a></li>
				<li><a class="dropdown-item" href="#">ريال</a></li>
		    </ul>
		</li>
    </ul>
    <ul class="navbar-nav">
		<li><a href="#" class="nav-link"> <i class="fa fa-envelope"></i> البريد الإلكتروني</a></li>
		<li><a href="#" class="nav-link"> <i class="fa fa-phone"></i> اتصل بنا</a></li>
	</ul>
  </div>
</div>
</nav>

<section class="header-main border-bottom">
	<div class="container">
		<div class="row align-items-center">
			<div class="col-lg-2 col-md-3 col-6">
				<a href="{% url 'store:home' %}" class="brand-wrap">
					<img class="logo" src="{% static './images/logo.png' %}">
				</a>
			</div>
			<div class="col-lg col-sm col-md col-6 flex-grow-0">
				<div class="category-wrap dropdown d-inline-block float-right">
					<button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown">
						<i class="fa fa-bars"></i> كل الفئات
					</button>
					<div class="dropdown-menu">
						{% for category in links %}
							<a class="dropdown-item" href="{{ category.get_url }}">{{ category.name }}</a>
						{% endfor %}
					</div>
				</div>
			</div>
			<div class="col-lg  col-md-6 col-sm-12 col">
				<form action="{% url 'store:search' %}" class="search" method="GET">
					<div class="input-group w-100">
					    <input type="text" class="form-control" style="width:60%;" placeholder="بحث" name="keyword">
					    <div class="input-group-append">
					      <button class="btn btn-primary" type="submit">
					        <i class="fa fa-search"></i>
					      </button>
					    </div>
				    </div>
				</form>
			</div>
			<div class="col-lg-3 col-sm-6 col-8 order-2 order-lg-3">
				<div class="d-flex justify-content-end mb-3 mb-lg-0">
                    {% if user.id is None %}
					<div class="widget-header">
						<small class="title text-muted">مرحباً بك!</small>
						<div>
							<a href="{% url 'accounts:login' %}">تسجيل الدخول</a> <span class="dark-transp">|</span>
							<a href="{% url 'accounts:register' %}">تسجيل جديد</a>
						</div>
					</div>
                    {% else %}
                    <div class="widget-header">
						<small class="title text-muted">مرحباً {{ user.first_name }}!</small>
						<div>
							<a href="{% url 'orders:my_orders' %}">طلباتي</a> <span class="dark-transp">|</span>
							<a href="{% url 'accounts:dashboard' %}">لوحة التحكم</a> <span class="dark-transp">|</span>
							<a href="{% url 'accounts:logout' %}">تسجيل خروج</a>
						</div>
					</div>
                    {% endif %}
					<a href="{% url 'cart:cart' %}" class="widget-header pl-3 ml-3">
						<div class="icon icon-sm rounded-circle border"><i class="fa fa-shopping-cart"></i></div>
						<span class="badge badge-pill badge-danger notify">{{ cart_count }}</span>
					</a>
				</div>
			</div>
		</div>
	</div>
</section>
</header>