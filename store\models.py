from django.db import models
from django.urls import reverse
from accounts.models import Account  # تغيير الاستيراد

class Category(models.Model):
    name = models.CharField('الاسم', max_length=50, unique=True)
    slug = models.SlugField('الرابط', max_length=100, unique=True)
    description = models.TextField('الوصف', max_length=255, blank=True)
    image = models.ImageField('الصورة', upload_to='categories', blank=True)

    class Meta:
        verbose_name = 'فئة'
        verbose_name_plural = 'الفئات'

    def get_url(self):
        return reverse('store:products_by_category', args=[self.slug])

    def __str__(self):
        return self.name

class Product(models.Model):
    name = models.CharField('اسم المنتج', max_length=200, unique=True)
    slug = models.SlugField('الرابط', max_length=200, unique=True)
    description = models.TextField('الوصف', max_length=500, blank=True)
    price = models.DecimalField('السعر', max_digits=10, decimal_places=2)
    image = models.ImageField('الصورة', upload_to='products')
    stock = models.IntegerField('المخزون')
    is_available = models.BooleanField('متوفر', default=True)
    category = models.ForeignKey(Category, verbose_name='الفئة', on_delete=models.CASCADE)
    created_date = models.DateTimeField('تاريخ الإنشاء', auto_now_add=True)
    modified_date = models.DateTimeField('تاريخ التعديل', auto_now=True)

    class Meta:
        verbose_name = 'منتج'
        verbose_name_plural = 'المنتجات'

    def get_url(self):
        if not self.slug or not self.category or not self.category.slug:
            return '#'  # Return a safe URL if slug is missing
        return reverse('store:product_detail', args=[self.category.slug, self.slug])

    def __str__(self):
        return self.name

class ReviewRating(models.Model):
    product = models.ForeignKey(Product, verbose_name='المنتج', on_delete=models.CASCADE)
    user = models.ForeignKey(Account, verbose_name='المستخدم', on_delete=models.CASCADE)
    subject = models.CharField('الموضوع', max_length=100, blank=True)
    review = models.TextField('المراجعة', max_length=500, blank=True)
    rating = models.FloatField('التقييم')
    status = models.BooleanField('نشط', default=True)
    created_at = models.DateTimeField('تاريخ الإنشاء', auto_now_add=True)
    updated_at = models.DateTimeField('تاريخ التحديث', auto_now=True)

    class Meta:
        verbose_name = 'تقييم'
        verbose_name_plural = 'التقييمات'

    def __str__(self):
        return self.subject
