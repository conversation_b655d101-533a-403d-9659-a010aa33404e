from django.shortcuts import render, redirect, get_object_or_404
from .models import Account
from django.contrib import messages, auth
from django.contrib.auth.decorators import login_required
from .forms import RegistrationForm, UserProfileForm, UserForm
from .models import Account, UserProfile
from orders.models import Order, OrderProduct

def register(request):
    if request.method == 'POST':
        first_name = request.POST['first_name']
        last_name = request.POST['last_name']
        email = request.POST['email']
        phone_number = request.POST['phone_number']
        password = request.POST['password']
        username = email.split("@")[0]

        if Account.objects.filter(email=email).exists():
            messages.error(request, 'البريد الإلكتروني مسجل مسبقاً')
            return redirect('accounts:register')

        user = Account.objects.create_user(
            first_name=first_name,
            last_name=last_name,
            email=email,
            username=username,
            password=password
        )
        user.phone_number = phone_number
        user.save()

        messages.success(request, 'تم التسجيل بنجاح')
        return redirect('accounts:login')

    return render(request, 'accounts/register.html')

def login(request):
    if request.method == 'POST':
        email = request.POST['email']
        password = request.POST['password']

        user = auth.authenticate(email=email, password=password)

        if user is not None:
            auth.login(request, user)
            messages.success(request, 'تم تسجيل الدخول بنجاح')
            return redirect('store:home')
        else:
            messages.error(request, 'بيانات الدخول غير صحيحة')
            return redirect('accounts:login')

    return render(request, 'accounts/signin.html')

@login_required(login_url='accounts:login')
def logout(request):
    auth.logout(request)
    messages.success(request, 'تم تسجيل الخروج')
    return redirect('store:home')

@login_required(login_url='login')
def dashboard(request):
    # جلب جميع الطلبات للمستخدم
    orders = Order.objects.filter(user=request.user, is_ordered=True).order_by('-created_at')
    orders_count = orders.count()

    # حساب الطلبات المكتملة
    completed_orders = orders.filter(status='مكتمل').count()

    # حساب الطلبات قيد المعالجة
    pending_orders = orders.filter(status='قيد المعالجة').count()

    # آخر 5 طلبات
    recent_orders = orders[:5]

    context = {
        'orders_count': orders_count,
        'completed_orders': completed_orders,
        'pending_orders': pending_orders,
        'recent_orders': recent_orders,
    }
    return render(request, 'accounts/dashboard.html', context)

@login_required(login_url='login')
def my_orders(request):
    orders = Order.objects.filter(user=request.user, is_ordered=True).order_by('-created_at')
    context = {
        'orders': orders,
    }
    return render(request, 'accounts/my_orders.html', context)

@login_required(login_url='login')
def edit_profile(request):
    # إنشاء أو جلب UserProfile إذا لم يكن موجود
    userprofile, created = UserProfile.objects.get_or_create(user=request.user)

    if request.method == 'POST':
        # تحديث بيانات المستخدم الأساسية
        request.user.first_name = request.POST.get('first_name', '')
        request.user.last_name = request.POST.get('last_name', '')
        request.user.email = request.POST.get('email', '')
        request.user.phone_number = request.POST.get('phone_number', '')
        request.user.save()

        # تحديث بيانات الملف الشخصي
        userprofile.address = request.POST.get('address', '')
        userprofile.gender = request.POST.get('gender', '')

        # تحديث تاريخ الميلاد إذا تم إدخاله
        date_of_birth = request.POST.get('date_of_birth', '')
        if date_of_birth:
            userprofile.date_of_birth = date_of_birth

        userprofile.save()

        messages.success(request, 'تم تحديث ملفك الشخصي بنجاح.')
        return redirect('accounts:edit_profile')

    context = {
        'userprofile': userprofile,
    }
    return render(request, 'accounts/edit_profile.html', context)

@login_required(login_url='login')
def change_password(request):
    if request.method == 'POST':
        current_password = request.POST['current_password']
        new_password = request.POST['new_password']
        confirm_password = request.POST['confirm_password']

        user = Account.objects.get(username__exact=request.user.username)

        if new_password == confirm_password:
            success = user.check_password(current_password)
            if success:
                user.set_password(new_password)
                user.save()
                messages.success(request, 'تم تغيير كلمة المرور بنجاح.')
                return redirect('change_password')
            else:
                messages.error(request, 'كلمة المرور الحالية غير صحيحة')
                return redirect('change_password')
        else:
            messages.error(request, 'كلمة المرور غير متطابقة!')
            return redirect('change_password')
    return render(request, 'accounts/change_password.html')

@login_required(login_url='login')
def order_detail(request, order_id):
    order_detail = OrderProduct.objects.filter(order__order_number=order_id)
    order = Order.objects.get(order_number=order_id)
    subtotal = 0
    for i in order_detail:
        subtotal += i.product_price * i.quantity

    context = {
        'order_detail': order_detail,
        'order': order,
        'subtotal': subtotal,
    }
    return render(request, 'accounts/order_detail.html', context)
