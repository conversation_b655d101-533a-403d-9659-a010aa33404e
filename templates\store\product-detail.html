{% load static %}
<!DOCTYPE HTML>
<html lang="ar" dir="rtl">
<head>
<meta charset="utf-8">
<meta http-equiv="pragma" content="no-cache" />
<meta http-equiv="cache-control" content="max-age=604800" />
<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

<title>جريت كارت | تفاصيل المنتج</title>

<link href="images/favicon.ico" rel="shortcut icon" type="image/x-icon">

<!-- jQuery -->
<script src="js/jquery-2.0.0.min.js" type="text/javascript"></script>

<!-- Bootstrap4 files-->
<script src="js/bootstrap.bundle.min.js" type="text/javascript"></script>
<link href="css/bootstrap.css" rel="stylesheet" type="text/css"/>

<!-- Font awesome 5 -->
<link href="fonts/fontawesome/css/all.min.css" type="text/css" rel="stylesheet">

<!-- custom style -->
<link href="css/ui.css" rel="stylesheet" type="text/css"/>
<link href="css/responsive.css" rel="stylesheet" media="only screen and (max-width: 1200px)" />
<link href="css/rtl-arabic.css" rel="stylesheet" type="text/css"/>

<!-- custom javascript -->
<script src="js/script.js" type="text/javascript"></script>
<script src="js/arabic-translation.js" type="text/javascript"></script>

<script type="text/javascript">
/// some script

// jquery ready start
$(document).ready(function() {
	// jQuery code

});
// jquery end
</script>

</head>
<body>


<header class="section-header">
<nav class="navbar p-md-0 navbar-expand-sm navbar-light border-bottom">
<div class="container">
  <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarTop4" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
    <span class="navbar-toggler-icon"></span>
  </button>
  <div class="collapse navbar-collapse" id="navbarTop4">
    <ul class="navbar-nav mr-auto">
    	<li class="nav-item dropdown">
		 	<a href="#" class="nav-link dropdown-toggle" data-toggle="dropdown">   Language </a>
		    <ul class="dropdown-menu small">
				<li><a class="dropdown-item" href="#">English</a></li>
				<li><a class="dropdown-item" href="#">Arabic</a></li>
				<li><a class="dropdown-item" href="#">Russian </a></li>
		    </ul>
		</li>
		<li class="nav-item dropdown">
			<a href="#" class="nav-link dropdown-toggle" data-toggle="dropdown"> USD </a>
			<ul class="dropdown-menu small">
				<li><a class="dropdown-item" href="#">EUR</a></li>
				<li><a class="dropdown-item" href="#">AED</a></li>
				<li><a class="dropdown-item" href="#">RUBL </a></li>
		    </ul>
		</li>
    </ul>
    <ul class="navbar-nav">
		<li><a href="#" class="nav-link"> <i class="fa fa-envelope"></i> Email </a></li>
		<li><a href="#" class="nav-link"> <i class="fa fa-phone"></i> Call us </a></li>
	</ul> <!-- list-inline //  -->
  </div> <!-- navbar-collapse .// -->
</div> <!-- container //  -->
</nav>

<section class="header-main border-bottom">
	<div class="container">
<div class="row align-items-center">
	<div class="col-lg-2 col-md-3 col-6">
		<a href="./" class="brand-wrap">
			<img class="logo" src="./images/logo.png">
		</a> <!-- brand-wrap.// -->
	</div>
	<div class="col-lg col-sm col-md col-6 flex-grow-0">
		<div class="category-wrap dropdown d-inline-block float-right">
			<button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown">
				<i class="fa fa-bars"></i> All category
			</button>
			<div class="dropdown-menu">
				<a class="dropdown-item" href="#">Machinery / Mechanical Parts / Tools </a>
				<a class="dropdown-item" href="#">Consumer Electronics / Home Appliances </a>
				<a class="dropdown-item" href="#">Auto / Transportation</a>
				<a class="dropdown-item" href="#">Apparel / Textiles / Timepieces </a>
				<a class="dropdown-item" href="#">Home & Garden / Construction / Lights </a>
				<a class="dropdown-item" href="#">Beauty & Personal Care / Health </a>
			</div>
		</div>  <!-- category-wrap.// -->
	</div> <!-- col.// -->
	<a href="./store.html" class="btn btn-outline-primary">Store</a>
	<div class="col-lg  col-md-6 col-sm-12 col">
		<form action="#" class="search">
			<div class="input-group w-100">
			    <input type="text" class="form-control" style="width:60%;" placeholder="Search">

			    <div class="input-group-append">
			      <button class="btn btn-primary" type="submit">
			        <i class="fa fa-search"></i>
			      </button>
			    </div>
		    </div>
		</form> <!-- search-wrap .end// -->
	</div> <!-- col.// -->
	<div class="col-lg-3 col-sm-6 col-8 order-2 order-lg-3">
				<div class="d-flex justify-content-end mb-3 mb-lg-0">
					<div class="widget-header">
						<small class="title text-muted">Welcome guest!</small>
						<div>
							<a href="./signin.html">Sign in</a> <span class="dark-transp"> | </span>
							<a href="./register.html"> Register</a>
						</div>
					</div>
					<a href="#" class="widget-header pl-3 ml-3">
						<div class="icon icon-sm rounded-circle border"><i class="fa fa-shopping-cart"></i></div>
						<span class="badge badge-pill badge-danger notify">0</span>
					</a>
				</div> <!-- widgets-wrap.// -->
			</div> <!-- col.// -->
</div> <!-- row.// -->
	</div> <!-- container.// -->
</section> <!-- header-main .// -->


</header> <!-- section-header.// -->



{% block title %}{{ single_product.name }} | جريت كارت{% endblock %}

{% block content %}
<section class="section-content padding-y bg">
<div class="container">
<!-- ============================ COMPONENT 1 ================================= -->
<div class="card">
	<div class="row no-gutters">
		<aside class="col-md-6">
			<article class="gallery-wrap">
				<div class="img-big-wrap">
					<a href="#">
						{% if single_product.image %}
							<img src="{{ single_product.image.url }}" alt="{{ single_product.name }}">
						{% else %}
							<img src="{% static 'images/default-product.png' %}" alt="{{ single_product.name }}">
						{% endif %}
					</a>
				</div>
			</article>
		</aside>
		<main class="col-md-6 border-right">
			<article class="content-body">
				<h2 class="title">{{ single_product.name }}</h2>

				<div class="mb-3">
					<var class="price h4">{{ single_product.price }} ريال</var>
				</div>

				<p>{{ single_product.description }}</p>

				<hr>
				<div class="row">
					<div class="item-option-select">
						<h6>اختر الحجم</h6>
						<select class="form-control" name="size">
							<option value="small">صغير</option>
							<option value="medium">متوسط</option>
							<option value="large">كبير</option>
						</select>
					</div>
				</div>
				<hr>
				{% if single_product.stock <= 0 %}
					<h5 class="text-danger">غير متوفر</h5>
				{% else %}
					{% if in_cart %}
						<a href="{% url 'cart:cart' %}" class="btn  btn-success"> <span class="text">في السلة</span> <i class="fas fa-check"></i></a>
						<a href="{% url 'cart:cart' %}" class="btn  btn-outline-primary"> <span class="text">عرض السلة</span> <i class="fas fa-eye"></i></a>
					{% else %}
						<a href="{% url 'cart:add_cart' single_product.id %}" class="btn  btn-primary"> <span class="text">أضف للسلة</span> <i class="fas fa-shopping-cart"></i></a>
					{% endif %}
				{% endif %}
			</article>
		</main>
	</div>
</div>

<!-- ===========================  REVIEWS  ============================== -->
<div class="row">
	<div class="col-md-9">
		<header class="section-heading">
			<h3>تقييمات المنتج </h3>
		</header>
		{% for review in reviews %}
		<article class="box mb-3">
			<div class="icontext w-100">
				<div class="text">
					<span class="date text-muted float-md-left">{{ review.updated_at }}</span>
					<h6 class="mb-1">{{ review.user.full_name }}</h6>
					<div class="rating-star">
						<span>
							<i class="fa fa-star{% if review.rating == 0.5 %}-half-o{% elif review.rating < 1 %}-o {% endif %}" aria-hidden="true"></i>
							<i class="fa fa-star{% if review.rating == 1.5 %}-half-o{% elif review.rating < 2 %}-o {% endif %}" aria-hidden="true"></i>
							<i class="fa fa-star{% if review.rating == 2.5 %}-half-o{% elif review.rating < 3 %}-o {% endif %}" aria-hidden="true"></i>
							<i class="fa fa-star{% if review.rating == 3.5 %}-half-o{% elif review.rating < 4 %}-o {% endif %}" aria-hidden="true"></i>
							<i class="fa fa-star{% if review.rating == 4.5 %}-half-o{% elif review.rating < 5 %}-o {% endif %}" aria-hidden="true"></i>
						</span>
					</div>
				</div>
			</div>
			<div class="mt-3">
				<h6>{{ review.subject }}</h6>
				<p>{{ review.review }}</p>
			</div>
		</article>
		{% endfor %}

		{% if user.is_authenticated %}
			<form action="{% url 'store:submit_review' single_product.id %}" method="POST">
				{% csrf_token %}
				<h5>اكتب تقييمك</h5>
				<div class="form-group">
					<label>التقييم</label>
					<br>
					<div class="rate">
						<input type="radio" name="rating" id="rating10" value="5" required /><label for="rating10" title="ممتاز"></label>
						<input type="radio" name="rating" id="rating9" value="4.5" required /><label for="rating9" title="4.5 نجوم"></label>
						<input type="radio" name="rating" id="rating8" value="4" required /><label for="rating8" title="جيد جداً"></label>
						<input type="radio" name="rating" id="rating7" value="3.5" required /><label for="rating7" title="3.5 نجوم"></label>
						<input type="radio" name="rating" id="rating6" value="3" required /><label for="rating6" title="جيد"></label>
						<input type="radio" name="rating" id="rating5" value="2.5" required /><label for="rating5" title="2.5 نجوم"></label>
						<input type="radio" name="rating" id="rating4" value="2" required /><label for="rating4" title="مقبول"></label>
						<input type="radio" name="rating" id="rating3" value="1.5" required /><label for="rating3" title="1.5 نجوم"></label>
						<input type="radio" name="rating" id="rating2" value="1" required /><label for="rating2" title="سيئ"></label>
						<input type="radio" name="rating" id="rating1" value="0.5" required /><label for="rating1" title="سيئ جداً"></label>
					</div>
				</div>
				<div class="form-group">
					<label>عنوان التقييم</label>
					<input type="text" class="form-control" name="subject" required>
				</div>
				<div class="form-group">
					<label>التقييم</label>
					<textarea name="review" rows="4" class="form-control" required></textarea>
				</div>
				<button type="submit" class="btn btn-primary">إرسال</button>
			</form>
		{% else %}
			<p>الرجاء <a href="{% url 'accounts:login' %}">تسجيل الدخول</a> لكتابة تقييم.</p>
		{% endif %}
	</div>
</div>
<!-- ===========================  REVIEWS END  ============================== -->

</div>
</section>
{% endblock %}

</body>
</html>