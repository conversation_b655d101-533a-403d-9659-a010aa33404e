from django.contrib import admin
from .models import Payment, Order, OrderProduct

class OrderProductInline(admin.TabularInline):
    model = OrderProduct
    readonly_fields = ('payment', 'user', 'product', 'quantity', 'product_price', 'ordered')
    extra = 0

class OrderAdmin(admin.ModelAdmin):
    list_display = ['order_number', 'full_name', 'phone', 'email', 'city', 'order_total', 'payment_method', 'status_badge', 'payment_status_badge', 'is_ordered', 'created_at']
    list_filter = ['status', 'is_ordered', 'created_at', 'payment__payment_method']
    search_fields = ['order_number', 'first_name', 'last_name', 'phone', 'email']
    list_per_page = 20
    inlines = [OrderProductInline]
    readonly_fields = ('order_number', 'created_at', 'updated_at')

    def payment_method(self, obj):
        if obj.payment:
            return obj.payment.payment_method
        return 'غير محدد'
    payment_method.short_description = 'طريقة الدفع'

    def status_badge(self, obj):
        color = obj.get_status_color()
        return f'<span class="badge badge-{color}">{obj.get_status_display()}</span>'
    status_badge.short_description = 'حالة الطلب'
    status_badge.allow_tags = True

    def payment_status_badge(self, obj):
        color = obj.get_payment_status_color()
        status = obj.get_payment_status_display()
        return f'<span class="badge badge-{color}">{status}</span>'
    payment_status_badge.short_description = 'حالة الدفع'
    payment_status_badge.allow_tags = True

    actions = ['mark_as_confirmed', 'mark_as_processing', 'mark_as_shipped', 'mark_as_delivered', 'mark_payment_completed', 'mark_as_paid', 'mark_as_completed']

    def mark_as_confirmed(self, request, queryset):
        from django.utils import timezone
        updated = queryset.update(status='Confirmed', confirmed_at=timezone.now())
        self.message_user(request, f'تم تأكيد {updated} طلب بنجاح.')
    mark_as_confirmed.short_description = 'تأكيد الطلبات المحددة'

    def mark_as_processing(self, request, queryset):
        updated = queryset.update(status='Processing')
        self.message_user(request, f'تم تحديث {updated} طلب إلى "قيد التحضير".')
    mark_as_processing.short_description = 'تحديث إلى "قيد التحضير"'

    def mark_as_shipped(self, request, queryset):
        from django.utils import timezone
        updated = queryset.update(status='Shipped', shipped_at=timezone.now())
        self.message_user(request, f'تم تحديث {updated} طلب إلى "تم الشحن".')
    mark_as_shipped.short_description = 'تحديث إلى "تم الشحن"'

    def mark_as_delivered(self, request, queryset):
        """تحديد الطلبات كـ تم التسليم وتحديث حالة الدفع للدفع عند الاستلام"""
        from django.utils import timezone
        updated = 0
        for order in queryset:
            if order.status != 'Delivered':
                order.status = 'Delivered'
                order.delivered_at = timezone.now()

                # للدفع عند الاستلام، تحديث حالة الدفع إلى مكتمل
                if order.is_cash_on_delivery() and order.payment:
                    order.payment.status = 'Completed'
                    order.payment.save()

                order.save()
                updated += 1

        self.message_user(request, f'تم تحديث {updated} طلب إلى "تم التسليم" وتحديث حالة الدفع')
    mark_as_delivered.short_description = 'تحديث إلى "تم التسليم"'

    def mark_payment_completed(self, request, queryset):
        """تحديد حالة الدفع كـ مكتمل وتحديث حالة الطلب إلى مكتمل"""
        updated = 0
        for order in queryset:
            if order.payment and order.is_cash_on_delivery():
                # تحديث حالة الدفع
                order.payment.status = 'Completed'
                order.payment.save()

                # تحديث حالة الطلب إلى "مكتمل"
                order.status = 'Completed'
                order.save()

                updated += 1

        self.message_user(request, f'تم تحديث حالة الدفع والطلب لـ {updated} طلب إلى "مكتمل"')
    mark_payment_completed.short_description = 'تحديد الدفع كـ مكتمل'

    def mark_as_paid(self, request, queryset):
        """تحديد الطلبات كـ مدفوعة"""
        updated = queryset.update(status='Paid')
        self.message_user(request, f'تم تحديث {updated} طلب إلى "مدفوع"')
    mark_as_paid.short_description = 'تحديث إلى "مدفوع"'

    def mark_as_completed(self, request, queryset):
        """تحديد الطلبات كـ مكتملة"""
        updated = queryset.update(status='Completed')
        self.message_user(request, f'تم تحديث {updated} طلب إلى "مكتمل"')
    mark_as_completed.short_description = 'تحديث إلى "مكتمل"'
    fieldsets = (
        ('معلومات الطلب', {
            'fields': ('order_number', 'user', 'status', 'is_ordered', 'created_at', 'updated_at')
        }),
        ('معلومات العميل', {
            'fields': ('first_name', 'last_name', 'phone', 'email')
        }),
        ('عنوان التوصيل', {
            'fields': ('address_line_1', 'address_line_2', 'city')
        }),
        ('تفاصيل الدفع', {
            'fields': ('payment', 'order_total', 'tax')
        }),
        ('التتبع والملاحظات', {
            'fields': ('confirmed_at', 'shipped_at', 'delivered_at', 'tracking_number', 'admin_notes'),
            'classes': ('collapse',)
        }),
        ('معلومات إضافية', {
            'fields': ('order_note', 'ip'),
            'classes': ('collapse',)
        }),
    )

class PaymentAdmin(admin.ModelAdmin):
    list_display = ['payment_id', 'user', 'payment_method', 'amount_paid', 'status', 'created_at']
    list_filter = ['status', 'payment_method']
    search_fields = ['payment_id', 'user__email']
    list_per_page = 20

class OrderProductAdmin(admin.ModelAdmin):
    list_display = ['order', 'payment', 'user', 'product', 'quantity', 'product_price', 'ordered', 'created_at']
    list_filter = ['ordered']
    search_fields = ['order__order_number', 'user__email', 'product__name']
    list_per_page = 20

admin.site.register(Payment, PaymentAdmin)
admin.site.register(Order, OrderAdmin)
admin.site.register(OrderProduct, OrderProductAdmin)