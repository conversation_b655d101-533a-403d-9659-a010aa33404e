from django.contrib import admin
from .models import Payment, Order, OrderProduct

class OrderProductInline(admin.TabularInline):
    model = OrderProduct
    readonly_fields = ('payment', 'user', 'product', 'quantity', 'product_price', 'ordered')
    extra = 0

class OrderAdmin(admin.ModelAdmin):
    list_display = ['order_number', 'full_name', 'phone', 'email', 'city', 'order_total', 'tax', 'status', 'is_ordered', 'created_at']
    list_filter = ['status', 'is_ordered']
    search_fields = ['order_number', 'first_name', 'last_name', 'phone', 'email']
    list_per_page = 20
    inlines = [OrderProductInline]
    fieldsets = (
        ('معلومات الطلب', {
            'fields': ('order_number', 'status', 'is_ordered')
        }),
        ('معلومات العميل', {
            'fields': ('user', 'first_name', 'last_name', 'phone', 'email')
        }),
        ('معلومات التوصيل', {
            'fields': ('address_line_1', 'address_line_2', 'city')
        }),
        ('معلومات الدفع', {
            'fields': ('payment', 'order_total', 'tax')
        }),
        ('ملاحظات إضافية', {
            'fields': ('order_note', 'ip')
        })
    )

class PaymentAdmin(admin.ModelAdmin):
    list_display = ['payment_id', 'user', 'payment_method', 'amount_paid', 'status', 'created_at']
    list_filter = ['status', 'payment_method']
    search_fields = ['payment_id', 'user__email']
    list_per_page = 20

class OrderProductAdmin(admin.ModelAdmin):
    list_display = ['order', 'payment', 'user', 'product', 'quantity', 'product_price', 'ordered', 'created_at']
    list_filter = ['ordered']
    search_fields = ['order__order_number', 'user__email', 'product__name']
    list_per_page = 20

admin.site.register(Payment, PaymentAdmin)
admin.site.register(Order, OrderAdmin)
admin.site.register(OrderProduct, OrderProductAdmin)