{% extends 'base.html' %}
{% load static %}

{% block title %}تتبع الطلب | جريت كارت{% endblock %}

{% block content %}
<!-- ========================= SECTION CONTENT ========================= -->
<section class="section-content padding-y bg">
<div class="container">

<div class="row justify-content-center">
	<div class="col-lg-8">
		{% if order %}
		<div class="card">
			<div class="card-header">
				<h4><i class="fas fa-search"></i> تتبع الطلب #{{ order.order_number }}</h4>
			</div>
			<div class="card-body">
				<!-- معلومات الطلب الأساسية -->
				<div class="row mb-4">
					<div class="col-md-6">
						<h6>معلومات الطلب:</h6>
						<p>
							<strong>رقم الطلب:</strong> {{ order.order_number }}<br>
							<strong>تاريخ الطلب:</strong> {{ order.created_at|date:"d/m/Y H:i" }}<br>
							<strong>الحالة:</strong> 
							<span class="badge badge-{{ order.get_status_color }}">{{ order.get_status_display }}</span><br>
							<strong>المبلغ الإجمالي:</strong> {{ order.order_total }} جنيه
						</p>
					</div>
					<div class="col-md-6">
						<h6>معلومات الدفع:</h6>
						<p>
							<strong>طريقة الدفع:</strong> {{ order.payment.payment_method }}<br>
							<strong>حالة الدفع:</strong> 
							{% if order.payment.status == 'Pending' %}
								<span class="badge badge-warning">{{ order.payment.get_status_display }}</span>
							{% else %}
								<span class="badge badge-success">{{ order.payment.get_status_display }}</span>
							{% endif %}<br>
							{% if order.tracking_number %}
							<strong>رقم التتبع:</strong> {{ order.tracking_number }}<br>
							{% endif %}
						</p>
					</div>
				</div>
				
				<!-- Timeline تتبع الطلب -->
				<div class="order-timeline">
					<h6>مراحل الطلب:</h6>
					<div class="timeline">
						<!-- تم الطلب -->
						<div class="timeline-item {% if order.created_at %}completed{% endif %}">
							<div class="timeline-marker">
								<i class="fas fa-shopping-cart"></i>
							</div>
							<div class="timeline-content">
								<h6>تم استلام الطلب</h6>
								{% if order.created_at %}
								<small class="text-muted">{{ order.created_at|date:"d/m/Y H:i" }}</small>
								{% endif %}
							</div>
						</div>
						
						<!-- تم التأكيد -->
						<div class="timeline-item {% if order.confirmed_at %}completed{% elif order.status == 'Confirmed' %}current{% endif %}">
							<div class="timeline-marker">
								<i class="fas fa-check"></i>
							</div>
							<div class="timeline-content">
								<h6>تم تأكيد الطلب</h6>
								{% if order.confirmed_at %}
								<small class="text-muted">{{ order.confirmed_at|date:"d/m/Y H:i" }}</small>
								{% elif order.status == 'Confirmed' %}
								<small class="text-warning">في انتظار التأكيد</small>
								{% endif %}
							</div>
						</div>
						
						<!-- قيد التحضير -->
						<div class="timeline-item {% if order.status == 'Processing' %}current{% elif order.status in 'Shipped,Delivered,Paid' %}completed{% endif %}">
							<div class="timeline-marker">
								<i class="fas fa-cogs"></i>
							</div>
							<div class="timeline-content">
								<h6>قيد التحضير</h6>
								{% if order.status == 'Processing' %}
								<small class="text-warning">جاري تحضير طلبك</small>
								{% endif %}
							</div>
						</div>
						
						<!-- تم الشحن -->
						<div class="timeline-item {% if order.shipped_at %}completed{% elif order.status == 'Shipped' %}current{% endif %}">
							<div class="timeline-marker">
								<i class="fas fa-truck"></i>
							</div>
							<div class="timeline-content">
								<h6>تم الشحن</h6>
								{% if order.shipped_at %}
								<small class="text-muted">{{ order.shipped_at|date:"d/m/Y H:i" }}</small>
								{% elif order.status == 'Shipped' %}
								<small class="text-warning">في الطريق إليك</small>
								{% endif %}
								{% if order.tracking_number and order.status == 'Shipped' %}
								<br><small><strong>رقم التتبع:</strong> {{ order.tracking_number }}</small>
								{% endif %}
							</div>
						</div>
						
						<!-- تم التسليم -->
						<div class="timeline-item {% if order.delivered_at %}completed{% elif order.status == 'Delivered' %}current{% endif %}">
							<div class="timeline-marker">
								<i class="fas fa-home"></i>
							</div>
							<div class="timeline-content">
								<h6>تم التسليم</h6>
								{% if order.delivered_at %}
								<small class="text-muted">{{ order.delivered_at|date:"d/m/Y H:i" }}</small>
								{% elif order.status == 'Delivered' %}
								<small class="text-success">تم التسليم بنجاح</small>
								{% endif %}
							</div>
						</div>
						
						<!-- تم الدفع (للدفع عند الاستلام) -->
						{% if order.is_cash_on_delivery %}
						<div class="timeline-item {% if order.payment.status == 'Completed' %}completed{% elif order.status == 'Delivered' %}current{% endif %}">
							<div class="timeline-marker">
								<i class="fas fa-money-bill"></i>
							</div>
							<div class="timeline-content">
								<h6>تم الدفع</h6>
								{% if order.payment.status == 'Completed' %}
								<small class="text-muted">تم استلام المبلغ</small>
								{% elif order.status == 'Delivered' %}
								<small class="text-warning">في انتظار الدفع</small>
								{% endif %}
							</div>
						</div>
						{% endif %}
					</div>
				</div>
				
				<!-- ملاحظات إضافية -->
				{% if order.admin_notes %}
				<div class="alert alert-info mt-4">
					<h6><i class="fas fa-info-circle"></i> ملاحظات:</h6>
					<p class="mb-0">{{ order.admin_notes }}</p>
				</div>
				{% endif %}
				
				<!-- معلومات التواصل -->
				<div class="alert alert-light mt-4">
					<h6><i class="fas fa-phone"></i> هل تحتاج مساعدة؟</h6>
					<p class="mb-0">
						يمكنك التواصل معنا على رقم: <strong>01234567890</strong><br>
						أو عبر البريد الإلكتروني: <strong><EMAIL></strong>
					</p>
				</div>
			</div>
		</div>
		
		<!-- أزرار التنقل -->
		<div class="text-center mt-4">
			<a href="{% url 'store:home' %}" class="btn btn-primary">العودة للصفحة الرئيسية</a>
			<a href="{% url 'store:store' %}" class="btn btn-outline-primary">متابعة التسوق</a>
		</div>
		
		{% else %}
		<!-- لم يتم العثور على الطلب -->
		<div class="card">
			<div class="card-body text-center">
				<i class="fas fa-search text-muted" style="font-size: 4rem;"></i>
				<h3 class="mt-3">لم يتم العثور على الطلب</h3>
				<p class="lead">تأكد من رقم الطلب وحاول مرة أخرى</p>
				
				<!-- نموذج البحث -->
				<form method="get" class="mt-4">
					<div class="input-group justify-content-center">
						<input type="text" name="order_number" class="form-control" style="max-width: 300px;" 
							   placeholder="أدخل رقم الطلب" value="{{ request.GET.order_number }}">
						<div class="input-group-append">
							<button class="btn btn-primary" type="submit">
								<i class="fas fa-search"></i> بحث
							</button>
						</div>
					</div>
				</form>
			</div>
		</div>
		{% endif %}
	</div>
</div>

</div> <!-- container .//  -->
</section>
<!-- ========================= SECTION CONTENT END// ========================= -->

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #e9ecef;
}

.timeline-item {
    position: relative;
    margin-bottom: 30px;
    padding-left: 40px;
}

.timeline-marker {
    position: absolute;
    left: -40px;
    top: 0;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: #e9ecef;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
}

.timeline-item.completed .timeline-marker {
    background: #28a745;
    color: white;
}

.timeline-item.current .timeline-marker {
    background: #ffc107;
    color: #212529;
}

.timeline-content h6 {
    margin-bottom: 5px;
    font-weight: 600;
}

.timeline-item.completed .timeline-content h6 {
    color: #28a745;
}

.timeline-item.current .timeline-content h6 {
    color: #ffc107;
}
</style>

{% endblock %}
