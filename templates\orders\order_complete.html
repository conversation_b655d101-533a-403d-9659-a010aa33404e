<!DOCTYPE HTML>
<html lang="ar" dir="rtl">
<head>
<meta charset="utf-8">
<meta http-equiv="pragma" content="no-cache" />
<meta http-equiv="cache-control" content="max-age=604800" />
<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

<title>جريت كارت | اكتمال الطلب</title>

<link href="images/favicon.ico" rel="shortcut icon" type="image/x-icon">

<!-- jQuery -->
<script src="js/jquery-2.0.0.min.js" type="text/javascript"></script>

<!-- Bootstrap4 files-->
<script src="js/bootstrap.bundle.min.js" type="text/javascript"></script>
<link href="css/bootstrap.css" rel="stylesheet" type="text/css"/>

<!-- Font awesome 5 -->
<link href="fonts/fontawesome/css/all.min.css" type="text/css" rel="stylesheet">

<!-- custom style -->
<link href="css/ui.css" rel="stylesheet" type="text/css"/>
<link href="css/responsive.css" rel="stylesheet" media="only screen and (max-width: 1200px)" />
<link href="css/rtl-arabic.css" rel="stylesheet" type="text/css"/>

<!-- custom javascript -->
<script src="js/script.js" type="text/javascript"></script>
<script src="js/arabic-translation.js" type="text/javascript"></script>

</head>
<body>

<div class="container" style="margin-top: 50px;">
    <center><i class="fas fa-check-circle" style="font-size: 72px;margin-bottom: 20px;color: #28A745;"></i></center>
    <h2 class="text-center">تم الدفع بنجاح</h2>
	<br>
	<div class="text-center">
		<a href="store.html" class="btn btn-success">تسوق المزيد</a>
	</div>
</div>

<div class="container" style="margin: 0 auto;width: 50%;padding: 50px;background: #f1f1f1;margin-top: 50px;margin-bottom: 50px;">
    <div class="row invoice row-printable">
        <div class="col-md-12">
            <div class="panel panel-default plain" id="dash_0">
                <div class="panel-body p30">
                    <div class="row">
                        <div class="col-lg-6">
                            <div class="invoice-logo"><img src="{{ '/images/logo.png' }}" alt="شعار الفاتورة" style="max-height: 40px;"></div>
                        </div>
                        <div class="col-lg-6">
                            <div class="invoice-from">
                                <ul class="list-unstyled text-right">
                                    <li><strong>تم إرسال الفاتورة إلى</strong></li>
                                    <li>{{ order.full_name }}</li>
                                    <li>{{ order.full_address }}</li>
                                    <li>{{ order.city }}, {{ order.state }}</li>
                                    <li>{{ order.country }}</li>
                                </ul>
                            </div>
                        </div>
                        <div class="col-lg-12">
                            <div class="invoice-details mt25">
                                <div class="well">
                                    <ul class="list-unstyled mb0">
                                        <li><strong>رقم الطلب:</strong> #{{ order.order_number }}</li>
                                        <li><strong>رقم المعاملة:</strong> {{ payment.payment_id }}</li>
                                        <li><strong>تاريخ الطلب:</strong> {{ order.created_at }}</li>
                                        <li><strong>الحالة:</strong> تم الدفع</li>
                                    </ul>
                                </div>
                            </div>
                           
                            <div class="invoice-items">
                                <div class="table-responsive" style="overflow: hidden; outline: none;" tabindex="0">
                                    <table class="table table-bordered">
                                        <thead>
                                            <tr>
                                                <th class="per70 text-center">الوصف</th>
                                                <th class="per5 text-center">الكمية</th>
                                                <th class="per25 text-center">المجموع</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                        {% for item in order_detail %}
                                            <tr>
                                                <td>{{ item.product.product_name }}</td>
                                                <td class="text-center">{{ item.quantity }}</td>
                                                <td class="text-center">{{ item.product_price }} ج.م</td>
                                            </tr>
                                        {% endfor %}
                                        </tbody>
                                        <tfoot>
                                            <tr>
                                                <th colspan="2" class="text-right">المجموع الفرعي:</th>
                                                <th class="text-center">{{ subtotal }} ج.م</th>
                                            </tr>
                                            <tr>
                                                <th colspan="2" class="text-right">الضريبة:</th>
                                                <th class="text-center">{{ order.tax }} ج.م</th>
                                            </tr>
                                            <tr>
                                                <th colspan="2" class="text-right">المجموع الكلي:</th>
                                                <th class="text-center">{{ order.order_total }} ج.م</th>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            </div>
                            <div class="invoice-footer mt25">
                                <p class="text-center">شكراً لتسوقك معنا!</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
