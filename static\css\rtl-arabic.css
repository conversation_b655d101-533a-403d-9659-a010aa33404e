/* ملف CSS لدعم اللغة العربية واتجاه RTL */

/* تعيين اتجاه الصفحة من اليمين إلى اليسار */
body, html {
    direction: rtl;
    text-align: right;
    font-family: '<PERSON><PERSON><PERSON>', 'Inter', sans-serif;
}

/* Navbar RTL */
.navbar-nav {
    padding-right: 0;
}

.navbar-nav .nav-item {
    margin-left: 1rem;
    margin-right: 0;
}

/* تعديل اتجاه العناصر المختلفة */
.navbar-nav, .dropdown-menu, .list-inline, .list-check {
    padding-right: 0;
}

/* عكس الهوامش والحشوات */
.mr-auto {
    margin-right: 0 !important;
    margin-left: auto !important;
}

.ml-auto {
    margin-left: 0 !important;
    margin-right: auto !important;
}

.mr-1, .mr-2, .mr-3, .mr-4, .mr-5 {
    margin-right: 0 !important;
    margin-left: 0.25rem !important;
}

.ml-1, .ml-2, .ml-3, .ml-4, .ml-5 {
    margin-left: 0 !important;
    margin-right: 0.25rem !important;
}

.pr-1, .pr-2, .pr-3, .pr-4, .pr-5 {
    padding-right: 0 !important;
    padding-left: 0.25rem !important;
}

.pl-1, .pl-2, .pl-3, .pl-4, .pl-5 {
    padding-left: 0 !important;
    padding-right: 0.25rem !important;
}

/* عكس اتجاه الأيقونات */
.fa-arrow-right:before {
    content: "\f060" !important;
}

.fa-arrow-left:before {
    content: "\f061" !important;
}

.fa-long-arrow-alt-right:before {
    content: "\f30a" !important;
}

.fa-long-arrow-alt-left:before {
    content: "\f30b" !important;
}

/* عكس اتجاه الأيقونات الإضافية (Chevrons, Angles) */
.fa-chevron-left:before {
    content: "\f054" !important; /* chevron-right */
}

.fa-chevron-right:before {
    content: "\f053" !important; /* chevron-left */
}

.fa-angle-left:before {
    content: "\f105" !important; /* angle-right */
}

.fa-angle-right:before {
    content: "\f104" !important; /* angle-left */
}

.fa-angle-double-left:before {
    content: "\f101" !important; /* angle-double-right */
}

.fa-angle-double-right:before {
    content: "\f100" !important; /* angle-double-left */
}

/* تعديل اتجاه عناصر القائمة المنسدلة */
.dropdown-menu {
    text-align: right;
}

/* تعديل اتجاه عناصر النموذج */
.form-control {
    text-align: right;
}

/* تعديل اتجاه الأزرار */
.float-right {
    float: left !important;
}

.float-left {
    float: right !important;
}

/* تعديل اتجاه عناصر التنقل */
.navbar-nav {
    direction: rtl;
}

/* تعديل اتجاه عناصر البحث */
.input-group .form-control:not(:last-child) {
    border-radius: 0 0.25rem 0.25rem 0;
}

.input-group .input-group-append {
    margin-right: -1px;
    margin-left: 0;
}

.input-group-append .btn {
    border-radius: 0.25rem 0 0 0.25rem;
}

/* تعديل اتجاه عناصر السلة */
.widget-header .icon {
    margin-left: 10px;
    margin-right: 0;
}

/* تعديل اتجاه عناصر المنتجات */
.card-product-grid .info-wrap {
    text-align: right;
}

/* تعديل اتجاه عناصر التذييل */
.footer-bottom .nav-link {
    text-align: right;
}

/* تعديل اتجاه عناصر الصفحة الرئيسية */
.section-intro, .section-content {
    text-align: right;
}

/* تعديل اتجاه عناصر التنقل الجانبي */
.card-category-list .card-category {
    text-align: right;
}

/* تعديل اتجاه عناصر التصفية */
.filter-content {
    text-align: right;
}

/* تعديل اتجاه عناصر المنتج التفصيلية */
.gallery-wrap, .info-main, .info-aside {
    text-align: right;
}

/* تعديل اتجاه عناصر السلة */
.table-shopping-cart .price {
    text-align: left;
}

/* تعديل اتجاه عناصر الدفع */
.card-header .title {
    text-align: right;
}

/* تعديل اتجاه عناصر صفحة اكتمال الطلب */
.invoice-details .list-unstyled li strong {
    float: right;
    margin-left: 5px; /* لإضافة مسافة بين العنوان والقيمة */
}

.invoice-items .table th,
.invoice-items .table td {
    text-align: right !important;
}

/* الإبقاء على محاذاة أعمدة السعر إلى اليسار */
.invoice-items .table th.text-center:last-child,
.invoice-items .table td.text-center:last-child,
.invoice-items .table tfoot th:last-child {
    text-align: left !important;
}

/* تعديل محاذاة تذييل الفاتورة */
.invoice-footer p {
    text-align: right !important;
}

/* إضافة خط عربي */
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap');

/* RTL Arabic Styles */
body {
    direction: rtl;
    text-align: right;
    font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
}

.dropdown-menu {
    text-align: right;
}

.navbar-nav {
    padding-right: 0;
}

.mr-auto {
    margin-left: auto !important;
    margin-right: 0 !important;
}

.ml-auto {
    margin-right: auto !important;
    margin-left: 0 !important;
}

.float-right {
    float: left !important;
}

.float-left {
    float: right !important;
}

.text-right {
    text-align: left !important;
}

.text-left {
    text-align: right !important;
}

.input-group > .form-control:not(:last-child) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-top-right-radius: 0.25rem;
    border-bottom-right-radius: 0.25rem;
}

.input-group > .input-group-append > .btn {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-top-left-radius: 0.25rem;
    border-bottom-left-radius: 0.25rem;
}

/* Fix padding and margins */
.mr-1, .mx-1 { margin-left: 0.25rem !important; margin-right: 0 !important; }
.mr-2, .mx-2 { margin-left: 0.5rem !important; margin-right: 0 !important; }
.mr-3, .mx-3 { margin-left: 1rem !important; margin-right: 0 !important; }
.mr-4, .mx-4 { margin-left: 1.5rem !important; margin-right: 0 !important; }
.mr-5, .mx-5 { margin-left: 3rem !important; margin-right: 0 !important; }

.ml-1, .mx-1 { margin-right: 0.25rem !important; margin-left: 0 !important; }
.ml-2, .mx-2 { margin-right: 0.5rem !important; margin-left: 0 !important; }
.ml-3, .mx-3 { margin-right: 1rem !important; margin-left: 0 !important; }
.ml-4, .mx-4 { margin-right: 1.5rem !important; margin-left: 0 !important; }
.ml-5, .mx-5 { margin-right: 3rem !important; margin-left: 0 !important; }

.pr-1, .px-1 { padding-left: 0.25rem !important; padding-right: 0 !important; }
.pr-2, .px-2 { padding-left: 0.5rem !important; padding-right: 0 !important; }
.pr-3, .px-3 { padding-left: 1rem !important; padding-right: 0 !important; }
.pr-4, .px-4 { padding-left: 1.5rem !important; padding-right: 0 !important; }
.pr-5, .px-5 { padding-left: 3rem !important; padding-right: 0 !important; }

.pl-1, .px-1 { padding-right: 0.25rem !important; padding-left: 0 !important; }
.pl-2, .px-2 { padding-right: 0.5rem !important; padding-left: 0 !important; }
.pl-3, .px-3 { padding-right: 1rem !important; padding-left: 0 !important; }
.pl-4, .px-4 { padding-right: 1.5rem !important; padding-left: 0 !important; }
.pl-5, .px-5 { padding-right: 3rem !important; padding-left: 0 !important; }

/* Fix icon directions */
.fa-chevron-right:before {
    content: "\f053";
}

.fa-chevron-left:before {
    content: "\f054";
}

/* Fix search box */
.search .input-group .form-control {
    border-radius: 0 4px 4px 0 !important;
}

.search .input-group .btn {
    border-radius: 4px 0 0 4px !important;
}

/* Fix category dropdown */
.category-wrap .dropdown-menu {
    right: 0;
    left: auto;
}

/* Search bar RTL */
.input-group .form-control {
    border-radius: 0 .25rem .25rem 0 !important;
}

.input-group .input-group-append .btn {
    border-radius: .25rem 0 0 .25rem !important;
}

/* Float adjustments */
.float-right {
    float: left !important;
}

.float-left {
    float: right !important;
}

.mr-auto {
    margin-left: auto !important;
    margin-right: 0 !important;
}

.ml-auto {
    margin-right: auto !important;
    margin-left: 0 !important;
}

/* Text alignment */
.text-left {
    text-align: right !important;
}

.text-right {
    text-align: left !important;
}

/* Margin and padding */
.mr-1, .mx-1 {
    margin-left: 0.25rem !important;
    margin-right: 0 !important;
}

.mr-2, .mx-2 {
    margin-left: 0.5rem !important;
    margin-right: 0 !important;
}

.mr-3, .mx-3 {
    margin-left: 1rem !important;
    margin-right: 0 !important;
}

.ml-1, .mx-1 {
    margin-right: 0.25rem !important;
    margin-left: 0 !important;
}

.ml-2, .mx-2 {
    margin-right: 0.5rem !important;
    margin-left: 0 !important;
}

.ml-3, .mx-3 {
    margin-right: 1rem !important;
    margin-left: 0 !important;
}

/* Cart and user widgets */
.widgets-wrap .widget-header {
    margin-left: 0.5rem;
    margin-right: 0;
}

/* Product cards */
.card-product-grid .info-wrap {
    text-align: right;
}

/* Pagination */
.pagination {
    padding-right: 0;
}

/* Footer */
.footer-bottom .text-md-right {
    text-align: left !important;
}

/* Form elements */
.form-group label {
    text-align: right;
}

/* Icons */
.fa, .fas {
    margin-left: 0.5rem;
    margin-right: 0;
}

/* Bootstrap grid RTL fix */
.offset-1 {
    margin-right: 8.333333%;
    margin-left: 0;
}

.offset-2 {
    margin-right: 16.666667%;
    margin-left: 0;
}

/* Category menu */
.category-wrap .dropdown-toggle::after {
    margin-right: 0.5em;
    margin-left: 0;
}

/* Product details page */
.gallery-wrap .thumbs-wrap {
    margin-right: 0;
    margin-left: 1rem;
}

/* Reviews section */
.rating-stars {
    direction: ltr;
    display: inline-block;
}

/* Order summary */
.summary-footer {
    text-align: left;
}

/* Checkout form */
.form-row > [class*='col-'] {
    padding-right: 5px;
    padding-left: 5px;
}

/* RTL Support for Arabic */
body {
    direction: rtl;
    text-align: right;
}

/* Navbar RTL */
.navbar-nav {
    padding-right: 0;
}

.navbar-nav .nav-item {
    margin-left: 1rem;
    margin-right: 0;
}

.dropdown-menu {
    text-align: right;
}

/* Search bar RTL */
.input-group .form-control {
    border-radius: 0 .25rem .25rem 0 !important;
}

.input-group .input-group-append .btn {
    border-radius: .25rem 0 0 .25rem !important;
}

/* Float adjustments */
.float-right {
    float: left !important;
}

.float-left {
    float: right !important;
}

.mr-auto {
    margin-left: auto !important;
    margin-right: 0 !important;
}

.ml-auto {
    margin-right: auto !important;
    margin-left: 0 !important;
}

/* Text alignment */
.text-left {
    text-align: right !important;
}

.text-right {
    text-align: left !important;
}

/* Cart and user widgets */
.widgets-wrap .widget-header {
    margin-left: 0.5rem;
    margin-right: 0;
}

/* Product cards */
.card-product-grid .info-wrap {
    text-align: right;
}

/* Product details page */
.gallery-wrap .thumbs-wrap {
    margin-right: 0;
    margin-left: 1rem;
}

/* Reviews section */
.rating-stars {
    direction: ltr;
    display: inline-block;
}

/* Order summary */
.summary-footer {
    text-align: left;
}

/* Form elements */
.form-group label {
    text-align: right;
}

/* Breadcrumb */
.breadcrumb-item + .breadcrumb-item::before {
    float: right;
    padding-left: 0.5rem;
    padding-right: 0.5rem;
}

/* Pagination */
.pagination {
    padding-right: 0;
    direction: ltr;
}

/* Card titles and content */
.card-title, .card-text {
    text-align: right;
}

/* List groups */
.list-group {
    padding-right: 0;
}

/* Table */
.table th, .table td {
    text-align: right;
}

/* Modal */
.modal-header .close {
    margin: -1rem auto -1rem -1rem;
}

/* Alert */
.alert-dismissible {
    padding-right: 1.25rem;
    padding-left: 4rem;
}

.alert-dismissible .close {
    left: 0;
    right: auto;
}

/* Input groups */
.input-group > .input-group-prepend > .btn,
.input-group > .input-group-prepend > .input-group-text,
.input-group > .input-group-append:not(:last-child) > .btn,
.input-group > .input-group-append:not(:last-child) > .input-group-text,
.input-group > .input-group-append:last-child > .btn:not(:last-child):not(.dropdown-toggle),
.input-group > .input-group-append:last-child > .input-group-text:not(:last-child) {
    border-radius: 0 .25rem .25rem 0;
}

.input-group > .input-group-append > .btn,
.input-group > .input-group-append > .input-group-text,
.input-group > .input-group-prepend:not(:first-child) > .btn,
.input-group > .input-group-prepend:not(:first-child) > .input-group-text,
.input-group > .input-group-prepend:first-child > .btn:not(:first-child),
.input-group > .input-group-prepend:first-child > .input-group-text:not(:first-child) {
    border-radius: .25rem 0 0 .25rem;
}

/* Fix for Font Awesome icons */
.fa, .fas, .far, .fab {
    margin-left: 0.3rem;
    margin-right: 0;
}